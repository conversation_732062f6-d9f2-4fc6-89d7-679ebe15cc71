# Codebase Structure

This document provides a detailed overview of the Money Lover Chat application codebase organization.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Directory Structure

### Root Level

```
money_lover_chat/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── assets/           # Application assets (e.g., localization files)
│   └── l10n/
│       ├── en.json
│       └── es.json
├── pubspec.yaml      # Dependencies and project configuration
└── ...
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   ├── transaction_model.dart
│   └── parse_result.dart
├── services/         # Business logic and data services
│   ├── parser/       # Hybrid ML parsing module
│   │   ├── mlkit_parser_service.dart
│   │   ├── fallback_parser_service.dart
│   │   └── learned_association_service.dart
│   ├── storage_service.dart
│   └── localization_service.dart
├── screens/          # UI screens
│   ├── chat_screen.dart
│   └── ...
├── widgets/          # Reusable UI components
│   ├── transaction_message.dart
│   ├── category_picker_dialog.dart
│   └── quick_reply_widget.dart
├── utils/            # Utility functions
│   └── currency_utils.dart
├── navigation/       # Navigation logic
│   └── app_navigation.dart
├── main.dart         # Application entry point
└── theme.dart        # App theme configuration
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models:
  - `Transaction`: Represents a financial transaction, including `amount` and `currencyCode`.
  - `Category`: Defines transaction categories.
  - `ChatMessage`: Represents a message in the chat, with support for quick replies.
  - `TransactionProvider`: Manages all transaction and chat message state using Provider.
- **parse_result.dart**:
  - `ParseResult`: A data transfer object from the parser service to the UI.
  - `ParseStatus`: An enum (`success`, `needsCategory`, `needsType`, `failed`) indicating the result of a parsing attempt.
- **localization_data.dart**:
  - `LocalizationData`: A model that holds the keywords and number formatting rules for a specific locale, loaded from the JSON asset files.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`. It is the foundation for services that need to store data, like the `LearnedAssociationService`.
- **localization_service.dart**: Loads and provides localization data from JSON files in `assets/l10n/`. This service supplies the `FallbackParserService` with locale-specific keywords and number formats, making the regex parser adaptable to different languages.
- **parser/mlkit_parser_service.dart**: The primary service for parsing user input. It orchestrates the hybrid system. **Crucially, it now first consults the `LearnedAssociationService` to check for user-corrected patterns.** If no learned pattern is found, it proceeds to use on-device ML Kit for entity extraction and falls back to the `FallbackParserService` if needed.
- **parser/fallback_parser_service.dart**: A regex-based parser that serves as a safety net when ML Kit is unavailable. It is designed to be fully localizable by dynamically building its regex patterns from the data provided by the `LocalizationService`.
- **parser/learned_association_service.dart**: The core of the app's learning capability. This service stores and retrieves associations between text patterns and transaction attributes (type, category). It learns from user corrections during "soft fails" and manual edits, allowing the app to bypass complex parsing for known user inputs, increasing speed and accuracy over time.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for formatting currency amounts and handling currency codes and symbols.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions. It interacts with the `MlKitParserService` and handles the conversational "soft fail" flows, which now trigger the `LearnedAssociationService`.
- **settings_screen.dart**: Allows users to configure app settings, including the default currency.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction within the chat history.
- **category_picker_dialog.dart**: A dialog for manually selecting a category when the parser cannot determine one automatically.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message, used for transaction type disambiguation.